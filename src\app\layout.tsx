import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import { Toaster } from "@/components/ui/sonner";
import { ThemeProvider } from "@/components/theme-provider";
import { StructuredData } from "@/components/structured-data";
import { PerformanceMonitor } from "@/components/performance-monitor";
import { GoogleAnalytics } from "@/components/google-analytics";
import { FloatingSocialShare } from "@/components/floating-social-share";
import {
  generatePersonSchema,
  generateWebsiteSchema,
  generateOrganizationSchema,
  generateProfessionalServiceSchema
} from "@/lib/metadata";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: {
    default: "Ashish <PERSON>mat - Full Stack Developer & UI/UX Designer",
    template: "%s | <PERSON><PERSON>"
  },
  description: "Passionate full-stack developer and UI/UX designer creating innovative digital experiences. Specializing in React, Next.js, TypeScript, and modern web technologies. Available for freelance projects and full-time opportunities.",
  keywords: [
    "Ashish Kamat",
    "Full Stack Developer",
    "UI/UX Designer",
    "React Developer",
    "Next.js Developer",
    "TypeScript Expert",
    "Frontend Developer",
    "Backend Developer",
    "Web Development",
    "Portfolio",
    "Nepal Developer",
    "Freelance Developer",
    "React Specialist",
    "JavaScript Developer",
    "Node.js Developer"
  ],
  authors: [{ name: "Ashish Kamat", url: "https://ashishkamat.com.np" }],
  creator: "Ashish Kamat",
  publisher: "Ashish Kamat",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL("https://ashishkamat.com.np"),
  alternates: {
    canonical: "/",
  },
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://ashishkamat.com.np",
    title: "Ashish Kamat - Full Stack Developer & UI/UX Designer",
    description: "Passionate full-stack developer and UI/UX designer creating innovative digital experiences. Specializing in React, Next.js, TypeScript, and modern web technologies.",
    siteName: "Ashish Kamat Portfolio",
    images: [
      {
        url: "/og-image.png",
        width: 1200,
        height: 630,
        alt: "Ashish Kamat - Full Stack Developer Portfolio",
      },
      {
        url: "/og-image-square.png",
        width: 1200,
        height: 1200,
        alt: "Ashish Kamat - Full Stack Developer",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "Ashish Kamat - Full Stack Developer & UI/UX Designer",
    description: "Passionate full-stack developer and UI/UX designer creating innovative digital experiences. Specializing in React, Next.js, TypeScript, and modern web technologies.",
    creator: "@ashishkamat07",
    images: ["/og-image.png"],
  },
  robots: {
    index: true,
    follow: true,
    nocache: true,
    googleBot: {
      index: true,
      follow: true,
      noimageindex: false,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  verification: {
    google: "your-google-verification-code", // Add your Google Search Console verification code
    // yandex: "your-yandex-verification-code", // Add if needed
    // yahoo: "your-yahoo-verification-code", // Add if needed
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const personSchema = generatePersonSchema();
  const websiteSchema = generateWebsiteSchema();
  const organizationSchema = generateOrganizationSchema();
  const serviceSchema = generateProfessionalServiceSchema();

  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <StructuredData data={[personSchema, websiteSchema, organizationSchema, serviceSchema]} />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <ThemeProvider
          attribute="class"
          defaultTheme="dark"
          enableSystem
          disableTransitionOnChange
        >
          <PerformanceMonitor />
          <GoogleAnalytics measurementId={process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID || ''} />
          <FloatingSocialShare
            title="Ashish Kamat - Full Stack Developer & UI/UX Designer Portfolio"
            description="Explore my portfolio showcasing modern web development projects built with React, Next.js, and TypeScript."
            hashtags={["webdev", "portfolio", "react", "nextjs", "typescript"]}
          />
          {children}
          <Toaster />
        </ThemeProvider>
      </body>
    </html>
  );
}
