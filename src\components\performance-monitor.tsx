"use client";

import { useEffect } from 'react';

// Core Web Vitals metrics
interface WebVitalsMetric {
  name: string;
  value: number;
  rating: 'good' | 'needs-improvement' | 'poor';
  delta: number;
  id: string;
}

// Performance observer for Core Web Vitals
function reportWebVitals(metric: WebVitalsMetric) {
  // Log to console in development
  if (process.env.NODE_ENV === 'development') {
    console.log(`[Performance] ${metric.name}:`, {
      value: metric.value,
      rating: metric.rating,
      delta: metric.delta,
    });
  }

  // Send to Google Analytics
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', metric.name, {
      event_category: 'Web Vitals',
      event_label: metric.id,
      value: Math.round(metric.name === 'CLS' ? metric.value * 1000 : metric.value),
      non_interaction: true,
      custom_map: {
        metric_rating: metric.rating,
        metric_delta: metric.delta,
      },
    });

    // Also send as a custom event for better tracking
    window.gtag('event', 'core_web_vital', {
      event_category: 'Performance',
      event_label: `${metric.name} - ${metric.rating}`,
      value: Math.round(metric.name === 'CLS' ? metric.value * 1000 : metric.value),
      non_interaction: true,
    });
  }

  // Send to Vercel Analytics if available
  if (typeof window !== 'undefined' && (window as any).va) {
    (window as any).va('track', 'Web Vitals', {
      metric: metric.name,
      value: metric.value,
      rating: metric.rating,
    });
  }

  // Custom analytics endpoint
  if (process.env.NODE_ENV === 'production') {
    fetch('/api/analytics/web-vitals', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        metric: metric.name,
        value: metric.value,
        rating: metric.rating,
        url: window.location.href,
        timestamp: Date.now(),
      }),
    }).catch(console.error);
  }
}

// Performance monitoring hook
export function usePerformanceMonitoring() {
  useEffect(() => {
    // Only run in browser
    if (typeof window === 'undefined') return;

    // Import web-vitals dynamically
    import('web-vitals').then(({ onCLS, onFID, onFCP, onLCP, onTTFB, onINP }) => {
      onCLS(reportWebVitals);
      onFID(reportWebVitals);
      onFCP(reportWebVitals);
      onLCP(reportWebVitals);
      onTTFB(reportWebVitals);
      onINP(reportWebVitals);
    }).catch(console.error);

    // Monitor page load performance
    const measurePageLoad = () => {
      if ('performance' in window) {
        const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
        
        if (navigation) {
          const metrics = {
            dns: navigation.domainLookupEnd - navigation.domainLookupStart,
            tcp: navigation.connectEnd - navigation.connectStart,
            request: navigation.responseStart - navigation.requestStart,
            response: navigation.responseEnd - navigation.responseStart,
            dom: navigation.domContentLoadedEventEnd - navigation.responseEnd,
            load: navigation.loadEventEnd - navigation.loadEventStart,
            total: navigation.loadEventEnd - navigation.navigationStart,
          };

          if (process.env.NODE_ENV === 'development') {
            console.log('[Performance] Page Load Metrics:', metrics);
          }

          // Send to analytics
          if (typeof window !== 'undefined' && window.gtag) {
            Object.entries(metrics).forEach(([key, value]) => {
              if (value > 0) {
                window.gtag('event', `page_load_${key}`, {
                  event_category: 'Performance',
                  value: Math.round(value),
                  non_interaction: true,
                });
              }
            });
          }
        }
      }
    };

    // Measure after page load
    if (document.readyState === 'complete') {
      measurePageLoad();
    } else {
      window.addEventListener('load', measurePageLoad);
    }

    // Monitor resource loading
    const measureResources = () => {
      if ('performance' in window) {
        const resources = performance.getEntriesByType('resource') as PerformanceResourceTiming[];
        
        const resourceMetrics = resources.reduce((acc, resource) => {
          const type = resource.initiatorType || 'other';
          if (!acc[type]) acc[type] = { count: 0, totalSize: 0, totalTime: 0 };
          
          acc[type].count++;
          acc[type].totalTime += resource.responseEnd - resource.startTime;
          
          // Estimate size from transfer size
          if (resource.transferSize) {
            acc[type].totalSize += resource.transferSize;
          }
          
          return acc;
        }, {} as Record<string, { count: number; totalSize: number; totalTime: number }>);

        if (process.env.NODE_ENV === 'development') {
          console.log('[Performance] Resource Metrics:', resourceMetrics);
        }
      }
    };

    // Measure resources after load
    setTimeout(measureResources, 1000);

    // Cleanup
    return () => {
      window.removeEventListener('load', measurePageLoad);
    };
  }, []);
}

// Performance monitoring component
export function PerformanceMonitor() {
  usePerformanceMonitoring();
  return null; // This component doesn't render anything
}

// Performance budget checker
export function checkPerformanceBudget() {
  if (typeof window === 'undefined' || !('performance' in window)) return;

  const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
  
  if (!navigation) return;

  const budgets = {
    FCP: 1800, // First Contentful Paint
    LCP: 2500, // Largest Contentful Paint
    FID: 100,  // First Input Delay
    CLS: 0.1,  // Cumulative Layout Shift
    TTFB: 800, // Time to First Byte
  };

  const warnings: string[] = [];

  // Check TTFB
  const ttfb = navigation.responseStart - navigation.requestStart;
  if (ttfb > budgets.TTFB) {
    warnings.push(`TTFB (${Math.round(ttfb)}ms) exceeds budget (${budgets.TTFB}ms)`);
  }

  // Check total load time
  const totalLoad = navigation.loadEventEnd - navigation.navigationStart;
  if (totalLoad > 3000) {
    warnings.push(`Total load time (${Math.round(totalLoad)}ms) exceeds 3s`);
  }

  if (warnings.length > 0 && process.env.NODE_ENV === 'development') {
    console.warn('[Performance Budget] Warnings:', warnings);
  }

  return warnings;
}

// Utility to measure custom metrics
export function measureCustomMetric(name: string, fn: () => void | Promise<void>) {
  const start = performance.now();
  
  const finish = () => {
    const duration = performance.now() - start;
    
    if (process.env.NODE_ENV === 'development') {
      console.log(`[Performance] ${name}: ${Math.round(duration)}ms`);
    }

    // Send to analytics
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'custom_metric', {
        event_category: 'Performance',
        event_label: name,
        value: Math.round(duration),
        non_interaction: true,
      });
    }
  };

  try {
    const result = fn();
    
    if (result instanceof Promise) {
      return result.finally(finish);
    } else {
      finish();
      return result;
    }
  } catch (error) {
    finish();
    throw error;
  }
}

// Type declarations for global objects
declare global {
  interface Window {
    gtag: (...args: any[]) => void;
  }
}
