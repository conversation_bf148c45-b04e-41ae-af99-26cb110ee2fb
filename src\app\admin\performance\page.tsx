import { <PERSON>ada<PERSON> } from "next";
import { Navigation } from "@/components/navigation";
import { Footer } from "@/components/footer";
import { PerformanceDashboard } from "@/components/performance-dashboard";

export const metadata: Metadata = {
  title: "Performance Dashboard - Ashish Kamat",
  description: "Monitor Core Web Vitals and performance metrics for the portfolio website.",
  robots: {
    index: false,
    follow: false,
  },
};

export default function PerformancePage() {
  return (
    <div className="min-h-screen">
      <Navigation />
      <main className="pt-16">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="mb-8">
            <h1 className="text-3xl font-bold mb-2">Performance Dashboard</h1>
            <p className="text-muted-foreground">
              Monitor Core Web Vitals and performance metrics for optimal user experience.
            </p>
          </div>
          <PerformanceDashboard />
        </div>
      </main>
      <Footer />
    </div>
  );
}
