"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import { 
  Share2, 
  Twitter, 
  Linkedin, 
  Facebook, 
  Copy, 
  Check,
  Mail
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { toast } from "sonner";

interface SocialShareProps {
  url: string;
  title: string;
  description?: string;
  hashtags?: string[];
  className?: string;
  size?: "sm" | "md" | "lg";
}

export function SocialShare({ 
  url, 
  title, 
  description = "", 
  hashtags = [],
  className = "",
  size = "md"
}: SocialShareProps) {
  const [copied, setCopied] = useState(false);
  
  const encodedUrl = encodeURIComponent(url);
  const encodedTitle = encodeURIComponent(title);
  const encodedDescription = encodeURIComponent(description);
  const hashtagString = hashtags.map(tag => `#${tag}`).join(' ');

  const shareLinks = {
    twitter: `https://twitter.com/intent/tweet?url=${encodedUrl}&text=${encodedTitle}&hashtags=${hashtags.join(',')}`,
    linkedin: `https://www.linkedin.com/sharing/share-offsite/?url=${encodedUrl}`,
    facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
    email: `mailto:?subject=${encodedTitle}&body=${encodedDescription}%0A%0A${encodedUrl}`,
  };

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(url);
      setCopied(true);
      toast.success("Link copied to clipboard!");
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      toast.error("Failed to copy link");
    }
  };

  const openShareWindow = (shareUrl: string) => {
    window.open(
      shareUrl,
      'share-dialog',
      'width=600,height=400,resizable=yes,scrollbars=yes'
    );
  };

  const iconSize = size === "sm" ? 16 : size === "lg" ? 20 : 18;
  const buttonSize = size === "sm" ? "sm" : size === "lg" ? "lg" : "default";

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button 
          variant="outline" 
          size={buttonSize}
          className={`gap-2 ${className}`}
        >
          <Share2 size={iconSize} />
          Share
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-48">
        <DropdownMenuItem
          onClick={() => openShareWindow(shareLinks.twitter)}
          className="cursor-pointer"
        >
          <Twitter size={16} className="mr-2 text-blue-400" />
          Share on Twitter
        </DropdownMenuItem>
        
        <DropdownMenuItem
          onClick={() => openShareWindow(shareLinks.linkedin)}
          className="cursor-pointer"
        >
          <Linkedin size={16} className="mr-2 text-blue-600" />
          Share on LinkedIn
        </DropdownMenuItem>
        
        <DropdownMenuItem
          onClick={() => openShareWindow(shareLinks.facebook)}
          className="cursor-pointer"
        >
          <Facebook size={16} className="mr-2 text-blue-500" />
          Share on Facebook
        </DropdownMenuItem>
        
        <DropdownMenuItem
          onClick={() => window.open(shareLinks.email)}
          className="cursor-pointer"
        >
          <Mail size={16} className="mr-2 text-gray-600" />
          Share via Email
        </DropdownMenuItem>
        
        <DropdownMenuItem
          onClick={copyToClipboard}
          className="cursor-pointer"
        >
          <motion.div
            initial={false}
            animate={{ scale: copied ? 1.1 : 1 }}
            transition={{ duration: 0.2 }}
            className="flex items-center"
          >
            {copied ? (
              <Check size={16} className="mr-2 text-green-500" />
            ) : (
              <Copy size={16} className="mr-2" />
            )}
            {copied ? "Copied!" : "Copy Link"}
          </motion.div>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

// Simplified inline social share buttons
interface InlineSocialShareProps {
  url: string;
  title: string;
  className?: string;
}

export function InlineSocialShare({ url, title, className = "" }: InlineSocialShareProps) {
  const encodedUrl = encodeURIComponent(url);
  const encodedTitle = encodeURIComponent(title);

  const shareLinks = {
    twitter: `https://twitter.com/intent/tweet?url=${encodedUrl}&text=${encodedTitle}`,
    linkedin: `https://www.linkedin.com/sharing/share-offsite/?url=${encodedUrl}`,
    facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
  };

  const openShareWindow = (shareUrl: string) => {
    window.open(
      shareUrl,
      'share-dialog',
      'width=600,height=400,resizable=yes,scrollbars=yes'
    );
  };

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <span className="text-sm text-muted-foreground mr-2">Share:</span>
      
      <Button
        variant="ghost"
        size="sm"
        onClick={() => openShareWindow(shareLinks.twitter)}
        className="p-2 h-8 w-8"
      >
        <Twitter size={16} className="text-blue-400" />
      </Button>
      
      <Button
        variant="ghost"
        size="sm"
        onClick={() => openShareWindow(shareLinks.linkedin)}
        className="p-2 h-8 w-8"
      >
        <Linkedin size={16} className="text-blue-600" />
      </Button>
      
      <Button
        variant="ghost"
        size="sm"
        onClick={() => openShareWindow(shareLinks.facebook)}
        className="p-2 h-8 w-8"
      >
        <Facebook size={16} className="text-blue-500" />
      </Button>
    </div>
  );
}
