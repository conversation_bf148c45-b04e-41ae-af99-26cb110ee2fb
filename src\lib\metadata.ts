import { Metadata } from 'next';

const baseUrl = 'https://ashishkamat.com.np';

interface GenerateMetadataProps {
  title?: string;
  description?: string;
  path?: string;
  type?: 'website' | 'article' | 'profile';
  publishedTime?: string;
  modifiedTime?: string;
  tags?: string[];
  image?: string;
  noIndex?: boolean;
}

export function generateMetadata({
  title,
  description,
  path = '',
  type = 'website',
  publishedTime,
  modifiedTime,
  tags = [],
  image,
  noIndex = false,
}: GenerateMetadataProps = {}): Metadata {
  const url = `${baseUrl}${path}`;
  
  // Generate dynamic OG image URL
  const ogImageUrl = image || `/api/og?title=${encodeURIComponent(title || 'Ashish Kamat')}&subtitle=${encodeURIComponent(description || 'Full Stack Developer & UI/UX Designer')}&type=${type === 'article' ? 'blog' : 'website'}`;
  
  const metadata: Metadata = {
    title,
    description,
    alternates: {
      canonical: url,
    },
    openGraph: {
      title: title || 'Ashish Kamat - Full Stack Developer & UI/UX Designer',
      description: description || 'Passionate full-stack developer and UI/UX designer creating innovative digital experiences.',
      url,
      siteName: 'Ashish Kamat Portfolio',
      type,
      images: [
        {
          url: ogImageUrl,
          width: 1200,
          height: 630,
          alt: title || 'Ashish Kamat Portfolio',
        },
      ],
      locale: 'en_US',
    },
    twitter: {
      card: 'summary_large_image',
      title: title || 'Ashish Kamat - Full Stack Developer & UI/UX Designer',
      description: description || 'Passionate full-stack developer and UI/UX designer creating innovative digital experiences.',
      creator: '@ashishkamat07',
      images: [ogImageUrl],
    },
  };

  // Add article-specific metadata
  if (type === 'article') {
    metadata.openGraph = {
      ...metadata.openGraph,
      type: 'article',
      publishedTime,
      modifiedTime,
      authors: ['Ashish Kamat'],
      tags,
    };
  }

  // Add robots directive
  if (noIndex) {
    metadata.robots = {
      index: false,
      follow: false,
    };
  }

  return metadata;
}

// Predefined metadata for common pages
export const homeMetadata = generateMetadata({
  title: 'Ashish Kamat - Full Stack Developer & UI/UX Designer',
  description: 'Passionate full-stack developer and UI/UX designer creating innovative digital experiences. Specializing in React, Next.js, TypeScript, and modern web technologies. Available for freelance projects.',
  path: '/',
});

export const aboutMetadata = generateMetadata({
  title: 'About - Ashish Kamat',
  description: 'Learn more about Ashish Kamat, a passionate full-stack developer with 3+ years of experience in React, Next.js, TypeScript, and modern web technologies.',
  path: '/about',
});

export const projectsMetadata = generateMetadata({
  title: 'Projects - Ashish Kamat',
  description: 'Explore my portfolio of web development projects including e-commerce platforms, task management apps, and modern web applications built with React, Next.js, and TypeScript.',
  path: '/projects',
});

export const blogMetadata = generateMetadata({
  title: 'Blog - Ashish Kamat',
  description: 'Read my thoughts on web development, React, Next.js, TypeScript, and modern frontend technologies. Tips, tutorials, and insights from a full-stack developer.',
  path: '/blog',
});

export const contactMetadata = generateMetadata({
  title: 'Contact - Ashish Kamat',
  description: 'Get in touch with Ashish Kamat for web development projects, collaborations, or job opportunities. Available for freelance work and full-time positions.',
  path: '/contact',
});

// Schema.org structured data generators
export function generatePersonSchema() {
  return {
    '@context': 'https://schema.org',
    '@type': 'Person',
    '@id': `${baseUrl}/#person`,
    name: 'Ashish Kamat',
    alternateName: 'AK',
    url: baseUrl,
    image: {
      '@type': 'ImageObject',
      url: `${baseUrl}/ashish.png`,
      width: 400,
      height: 400,
    },
    sameAs: [
      'https://github.com/ash-333',
      'https://linkedin.com/in/ashishkamat',
      'https://twitter.com/ashishkamat07',
    ],
    jobTitle: 'Full Stack Developer',
    description: 'Passionate full-stack developer and UI/UX designer creating innovative digital experiences. Specializing in React, Next.js, TypeScript, and modern web technologies.',
    worksFor: {
      '@type': 'Organization',
      name: 'Freelance',
      url: baseUrl,
    },
    address: {
      '@type': 'PostalAddress',
      addressLocality: 'Kathmandu',
      addressRegion: 'Bagmati',
      addressCountry: 'Nepal',
    },
    contactPoint: {
      '@type': 'ContactPoint',
      email: '<EMAIL>',
      telephone: '+9779810580378',
      contactType: 'professional',
      availableLanguage: ['English', 'Nepali'],
    },
    knowsAbout: [
      'React',
      'Next.js',
      'TypeScript',
      'JavaScript',
      'Node.js',
      'Web Development',
      'UI/UX Design',
      'Frontend Development',
      'Backend Development',
      'MongoDB',
      'PostgreSQL',
      'Tailwind CSS',
      'Framer Motion',
    ],
    hasOccupation: {
      '@type': 'Occupation',
      name: 'Full Stack Developer',
      occupationLocation: {
        '@type': 'Country',
        name: 'Nepal',
      },
      skills: [
        'React Development',
        'Next.js Development',
        'TypeScript Programming',
        'UI/UX Design',
        'Database Design',
        'API Development',
      ],
    },
    alumniOf: {
      '@type': 'EducationalOrganization',
      name: 'Your University Name', // Update with actual university
    },
  };
}

export function generateWebsiteSchema() {
  return {
    '@context': 'https://schema.org',
    '@type': 'WebSite',
    '@id': `${baseUrl}/#website`,
    name: 'Ashish Kamat Portfolio',
    alternateName: 'AK Portfolio',
    url: baseUrl,
    description: 'Portfolio website of Ashish Kamat, a full-stack developer and UI/UX designer specializing in React, Next.js, and modern web technologies.',
    inLanguage: 'en-US',
    isAccessibleForFree: true,
    author: {
      '@id': `${baseUrl}/#person`,
    },
    creator: {
      '@id': `${baseUrl}/#person`,
    },
    copyrightHolder: {
      '@id': `${baseUrl}/#person`,
    },
    copyrightYear: new Date().getFullYear(),
    dateCreated: '2024-01-01',
    dateModified: new Date().toISOString().split('T')[0],
    potentialAction: {
      '@type': 'SearchAction',
      target: `${baseUrl}/search?q={search_term_string}`,
      'query-input': 'required name=search_term_string',
    },
  };
}

export function generateBreadcrumbSchema(items: Array<{ name: string; url: string }>) {
  return {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: items.map((item, index) => ({
      '@type': 'ListItem',
      position: index + 1,
      name: item.name,
      item: item.url,
    })),
  };
}

export function generateOrganizationSchema() {
  return {
    '@context': 'https://schema.org',
    '@type': 'Organization',
    '@id': `${baseUrl}/#organization`,
    name: 'Ashish Kamat - Web Development Services',
    url: baseUrl,
    logo: {
      '@type': 'ImageObject',
      url: `${baseUrl}/ashish-profile.svg`,
      width: 200,
      height: 200,
    },
    image: `${baseUrl}/ashish.png`,
    description: 'Professional web development services specializing in React, Next.js, TypeScript, and modern web technologies.',
    founder: {
      '@id': `${baseUrl}/#person`,
    },
    contactPoint: {
      '@type': 'ContactPoint',
      email: '<EMAIL>',
      telephone: '+9779810580378',
      contactType: 'customer service',
      availableLanguage: ['English', 'Nepali'],
    },
    address: {
      '@type': 'PostalAddress',
      addressLocality: 'Kathmandu',
      addressRegion: 'Bagmati',
      addressCountry: 'Nepal',
    },
    sameAs: [
      'https://github.com/ashishkamat',
      'https://linkedin.com/in/ashishkamat',
      'https://twitter.com/ashishkamat07',
    ],
    areaServed: {
      '@type': 'Country',
      name: 'Worldwide',
    },
    serviceType: [
      'Web Development',
      'Frontend Development',
      'Backend Development',
      'UI/UX Design',
      'React Development',
      'Next.js Development',
    ],
  };
}

export function generateProfessionalServiceSchema() {
  return {
    '@context': 'https://schema.org',
    '@type': 'ProfessionalService',
    '@id': `${baseUrl}/#service`,
    name: 'Ashish Kamat - Web Development Services',
    url: baseUrl,
    description: 'Professional web development and UI/UX design services using modern technologies like React, Next.js, and TypeScript.',
    provider: {
      '@id': `${baseUrl}/#person`,
    },
    areaServed: {
      '@type': 'Country',
      name: 'Worldwide',
    },
    serviceType: 'Web Development',
    hasOfferCatalog: {
      '@type': 'OfferCatalog',
      name: 'Web Development Services',
      itemListElement: [
        {
          '@type': 'Offer',
          itemOffered: {
            '@type': 'Service',
            name: 'Frontend Development',
            description: 'Modern frontend development using React, Next.js, and TypeScript',
          },
        },
        {
          '@type': 'Offer',
          itemOffered: {
            '@type': 'Service',
            name: 'Backend Development',
            description: 'Scalable backend solutions using Node.js and modern databases',
          },
        },
        {
          '@type': 'Offer',
          itemOffered: {
            '@type': 'Service',
            name: 'UI/UX Design',
            description: 'User-centered design and interface development',
          },
        },
      ],
    },
  };
}

export function generateArticleSchema({
  title,
  description,
  url,
  publishedTime,
  modifiedTime,
  tags = [],
  readingTime,
}: {
  title: string;
  description: string;
  url: string;
  publishedTime: string;
  modifiedTime?: string;
  tags?: string[];
  readingTime?: string;
}) {
  return {
    '@context': 'https://schema.org',
    '@type': 'Article',
    '@id': url,
    headline: title,
    description,
    url,
    datePublished: publishedTime,
    dateModified: modifiedTime || publishedTime,
    author: {
      '@id': `${baseUrl}/#person`,
    },
    publisher: {
      '@id': `${baseUrl}/#organization`,
    },
    mainEntityOfPage: {
      '@type': 'WebPage',
      '@id': url,
    },
    image: {
      '@type': 'ImageObject',
      url: `/api/og?title=${encodeURIComponent(title)}&type=blog`,
      width: 1200,
      height: 630,
    },
    keywords: tags,
    inLanguage: 'en-US',
    isAccessibleForFree: true,
    ...(readingTime && {
      timeRequired: readingTime,
    }),
  };
}

export function generateCreativeWorkSchema({
  title,
  description,
  url,
  image,
  technologies = [],
  dateCreated,
  githubUrl,
  liveUrl,
}: {
  title: string;
  description: string;
  url: string;
  image?: string;
  technologies?: string[];
  dateCreated: string;
  githubUrl?: string;
  liveUrl?: string;
}) {
  return {
    '@context': 'https://schema.org',
    '@type': 'CreativeWork',
    '@id': url,
    name: title,
    description,
    url,
    dateCreated,
    creator: {
      '@id': `${baseUrl}/#person`,
    },
    author: {
      '@id': `${baseUrl}/#person`,
    },
    image: {
      '@type': 'ImageObject',
      url: image || `/api/og?title=${encodeURIComponent(title)}&type=project`,
      width: 1200,
      height: 630,
    },
    keywords: technologies,
    inLanguage: 'en-US',
    isAccessibleForFree: true,
    ...(githubUrl && {
      codeRepository: githubUrl,
    }),
    ...(liveUrl && {
      workExample: {
        '@type': 'WebSite',
        url: liveUrl,
      },
    }),
    programmingLanguage: technologies.filter(tech =>
      ['JavaScript', 'TypeScript', 'Python', 'Java', 'C++', 'Go', 'Rust'].includes(tech)
    ),
    runtimePlatform: technologies.filter(tech =>
      ['Node.js', 'React', 'Next.js', 'Vue.js', 'Angular'].includes(tech)
    ),
  };
}
