"use client";

import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Share2, X, Twitter, Linkedin, Facebook, Copy, Check } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { toast } from "sonner";

interface FloatingSocialShareProps {
  url?: string;
  title?: string;
  description?: string;
  hashtags?: string[];
  showAfterScroll?: number; // Show after scrolling this many pixels
}

export function FloatingSocialShare({
  url,
  title = "Check out this amazing portfolio!",
  description = "Ashish Kamat - Full Stack Developer & UI/UX Designer",
  hashtags = ["webdev", "portfolio", "react", "nextjs"],
  showAfterScroll = 500,
}: FloatingSocialShareProps) {
  const [isVisible, setIsVisible] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);
  const [copied, setCopied] = useState(false);

  const currentUrl = url || (typeof window !== 'undefined' ? window.location.href : '');

  useEffect(() => {
    const handleScroll = () => {
      const scrolled = window.scrollY;
      setIsVisible(scrolled > showAfterScroll);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [showAfterScroll]);

  const shareLinks = {
    twitter: `https://twitter.com/intent/tweet?url=${encodeURIComponent(currentUrl)}&text=${encodeURIComponent(title)}&hashtags=${hashtags.join(',')}`,
    linkedin: `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(currentUrl)}`,
    facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(currentUrl)}`,
  };

  const openShareWindow = (shareUrl: string) => {
    window.open(
      shareUrl,
      'share-dialog',
      'width=600,height=400,resizable=yes,scrollbars=yes'
    );
  };

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(currentUrl);
      setCopied(true);
      toast.success("Link copied to clipboard!");
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      toast.error("Failed to copy link");
    }
  };

  const handleToggle = () => {
    setIsExpanded(!isExpanded);
  };

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          className="fixed right-4 bottom-4 z-50"
          initial={{ opacity: 0, scale: 0.8, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.8, y: 20 }}
          transition={{ duration: 0.3 }}
        >
          <div className="relative">
            {/* Main Share Button */}
            <motion.div
              className="relative"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Button
                onClick={handleToggle}
                size="lg"
                className="rounded-full w-14 h-14 shadow-lg hover:shadow-xl transition-all duration-300 bg-primary hover:bg-primary/90"
              >
                <motion.div
                  animate={{ rotate: isExpanded ? 45 : 0 }}
                  transition={{ duration: 0.2 }}
                >
                  {isExpanded ? <X className="h-6 w-6" /> : <Share2 className="h-6 w-6" />}
                </motion.div>
              </Button>
            </motion.div>

            {/* Expanded Share Options */}
            <AnimatePresence>
              {isExpanded && (
                <motion.div
                  className="absolute bottom-16 right-0 flex flex-col gap-2"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: 20 }}
                  transition={{ duration: 0.2, staggerChildren: 0.05 }}
                >
                  {/* Twitter */}
                  <motion.div
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: 20 }}
                    transition={{ duration: 0.2 }}
                  >
                    <Button
                      onClick={() => openShareWindow(shareLinks.twitter)}
                      size="sm"
                      variant="outline"
                      className="rounded-full w-12 h-12 shadow-md hover:shadow-lg transition-all duration-300 bg-background/95 backdrop-blur-sm border-blue-400/50 hover:border-blue-400 hover:bg-blue-50 dark:hover:bg-blue-950/20"
                    >
                      <Twitter className="h-4 w-4 text-blue-400" />
                    </Button>
                  </motion.div>

                  {/* LinkedIn */}
                  <motion.div
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: 20 }}
                    transition={{ duration: 0.2, delay: 0.05 }}
                  >
                    <Button
                      onClick={() => openShareWindow(shareLinks.linkedin)}
                      size="sm"
                      variant="outline"
                      className="rounded-full w-12 h-12 shadow-md hover:shadow-lg transition-all duration-300 bg-background/95 backdrop-blur-sm border-blue-600/50 hover:border-blue-600 hover:bg-blue-50 dark:hover:bg-blue-950/20"
                    >
                      <Linkedin className="h-4 w-4 text-blue-600" />
                    </Button>
                  </motion.div>

                  {/* Facebook */}
                  <motion.div
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: 20 }}
                    transition={{ duration: 0.2, delay: 0.1 }}
                  >
                    <Button
                      onClick={() => openShareWindow(shareLinks.facebook)}
                      size="sm"
                      variant="outline"
                      className="rounded-full w-12 h-12 shadow-md hover:shadow-lg transition-all duration-300 bg-background/95 backdrop-blur-sm border-blue-500/50 hover:border-blue-500 hover:bg-blue-50 dark:hover:bg-blue-950/20"
                    >
                      <Facebook className="h-4 w-4 text-blue-500" />
                    </Button>
                  </motion.div>

                  {/* Copy Link */}
                  <motion.div
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: 20 }}
                    transition={{ duration: 0.2, delay: 0.15 }}
                  >
                    <Button
                      onClick={copyToClipboard}
                      size="sm"
                      variant="outline"
                      className="rounded-full w-12 h-12 shadow-md hover:shadow-lg transition-all duration-300 bg-background/95 backdrop-blur-sm border-gray-400/50 hover:border-gray-400 hover:bg-gray-50 dark:hover:bg-gray-950/20"
                    >
                      <motion.div
                        animate={{ scale: copied ? 1.2 : 1 }}
                        transition={{ duration: 0.2 }}
                      >
                        {copied ? (
                          <Check className="h-4 w-4 text-green-500" />
                        ) : (
                          <Copy className="h-4 w-4 text-gray-600" />
                        )}
                      </motion.div>
                    </Button>
                  </motion.div>
                </motion.div>
              )}
            </AnimatePresence>

            {/* Tooltip */}
            {!isExpanded && (
              <motion.div
                className="absolute bottom-16 right-0 bg-background/95 backdrop-blur-sm border border-border rounded-lg px-3 py-2 text-sm whitespace-nowrap shadow-lg"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 1 }}
              >
                Share this page
                <div className="absolute top-full right-4 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-border"></div>
              </motion.div>
            )}
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}

// Hook to automatically add floating share to pages
export function useFloatingShare(options?: Partial<FloatingSocialShareProps>) {
  return <FloatingSocialShare {...options} />;
}
