import { NextRequest, NextResponse } from 'next/server';

export const runtime = 'edge';

interface WebVitalsData {
  metric: string;
  value: number;
  rating: 'good' | 'needs-improvement' | 'poor';
  url: string;
  timestamp: number;
  userAgent?: string;
  connection?: string;
}

export async function POST(request: NextRequest) {
  try {
    const data: WebVitalsData = await request.json();
    
    // Validate the data
    if (!data.metric || typeof data.value !== 'number' || !data.url) {
      return NextResponse.json(
        { error: 'Invalid data format' },
        { status: 400 }
      );
    }

    // Add additional context
    const enrichedData = {
      ...data,
      userAgent: request.headers.get('user-agent') || '',
      ip: request.ip || request.headers.get('x-forwarded-for') || '',
      timestamp: data.timestamp || Date.now(),
    };

    // Log in development
    if (process.env.NODE_ENV === 'development') {
      console.log('[Web Vitals]', enrichedData);
    }

    // In production, you would typically:
    // 1. Store in a database (e.g., PostgreSQL, MongoDB)
    // 2. Send to analytics service (e.g., Google Analytics, Mixpanel)
    // 3. Send to monitoring service (e.g., DataDog, New Relic)
    
    // Example: Store in database
    // await db.webVitals.create({ data: enrichedData });
    
    // Example: Send to external analytics
    // await sendToAnalytics(enrichedData);

    // For now, we'll just log and return success
    return NextResponse.json({ success: true });
    
  } catch (error) {
    console.error('Error processing web vitals data:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Optional: GET endpoint to retrieve performance data
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const metric = searchParams.get('metric');
    const days = parseInt(searchParams.get('days') || '7');
    
    // In a real implementation, you would query your database
    // const data = await db.webVitals.findMany({
    //   where: {
    //     ...(metric && { metric }),
    //     timestamp: {
    //       gte: Date.now() - (days * 24 * 60 * 60 * 1000)
    //     }
    //   },
    //   orderBy: { timestamp: 'desc' }
    // });

    // For now, return mock data
    const mockData = [
      {
        metric: 'LCP',
        value: 1200,
        rating: 'good',
        url: '/',
        timestamp: Date.now() - 3600000,
      },
      {
        metric: 'FID',
        value: 50,
        rating: 'good',
        url: '/',
        timestamp: Date.now() - 7200000,
      },
    ];

    return NextResponse.json({ data: mockData });
    
  } catch (error) {
    console.error('Error retrieving web vitals data:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
