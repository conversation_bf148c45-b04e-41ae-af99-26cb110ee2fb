"use client";

import { useEffect } from 'react';
import Script from 'next/script';

interface GoogleAnalyticsProps {
  measurementId: string;
}

export function GoogleAnalytics({ measurementId }: GoogleAnalyticsProps) {
  useEffect(() => {
    // Initialize gtag if not already done
    if (typeof window !== 'undefined' && !window.gtag) {
      window.dataLayer = window.dataLayer || [];
      window.gtag = function gtag() {
        window.dataLayer.push(arguments);
      };
      window.gtag('js', new Date());
      window.gtag('config', measurementId, {
        page_title: document.title,
        page_location: window.location.href,
      });
    }
  }, [measurementId]);

  // Don't load in development
  if (process.env.NODE_ENV === 'development') {
    return null;
  }

  return (
    <>
      <Script
        src={`https://www.googletagmanager.com/gtag/js?id=${measurementId}`}
        strategy="afterInteractive"
      />
      <Script id="google-analytics" strategy="afterInteractive">
        {`
          window.dataLayer = window.dataLayer || [];
          function gtag(){dataLayer.push(arguments);}
          gtag('js', new Date());
          gtag('config', '${measurementId}', {
            page_title: document.title,
            page_location: window.location.href,
          });
        `}
      </Script>
    </>
  );
}

// Analytics utility functions
export const analytics = {
  // Track page views
  pageView: (url: string, title?: string) => {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('config', process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID!, {
        page_title: title || document.title,
        page_location: url,
      });
    }
  },

  // Track events
  event: (action: string, category: string, label?: string, value?: number) => {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', action, {
        event_category: category,
        event_label: label,
        value: value,
      });
    }
  },

  // Track custom events
  trackClick: (elementName: string, location?: string) => {
    analytics.event('click', 'engagement', `${elementName}${location ? ` - ${location}` : ''}`);
  },

  trackDownload: (fileName: string) => {
    analytics.event('download', 'file', fileName);
  },

  trackContact: (method: string) => {
    analytics.event('contact', 'engagement', method);
  },

  trackProjectView: (projectName: string) => {
    analytics.event('view_project', 'portfolio', projectName);
  },

  trackBlogRead: (articleTitle: string, readTime?: number) => {
    analytics.event('read_article', 'blog', articleTitle, readTime);
  },

  trackSearch: (query: string, results: number) => {
    analytics.event('search', 'site_search', query, results);
  },

  trackShare: (platform: string, content: string) => {
    analytics.event('share', 'social', `${platform} - ${content}`);
  },

  // Track performance metrics
  trackPerformance: (metric: string, value: number, rating: string) => {
    analytics.event('performance_metric', 'core_web_vitals', `${metric} - ${rating}`, value);
  },

  // Track user engagement
  trackEngagement: (action: string, duration?: number) => {
    analytics.event(action, 'user_engagement', undefined, duration);
  },
};

// Hook for tracking page views in Next.js
export function usePageTracking() {
  useEffect(() => {
    const handleRouteChange = (url: string) => {
      analytics.pageView(url);
    };

    // Track initial page load
    analytics.pageView(window.location.href);

    // Listen for route changes (if using Next.js router)
    if (typeof window !== 'undefined') {
      window.addEventListener('popstate', () => {
        handleRouteChange(window.location.href);
      });
    }

    return () => {
      if (typeof window !== 'undefined') {
        window.removeEventListener('popstate', () => {
          handleRouteChange(window.location.href);
        });
      }
    };
  }, []);
}

// Enhanced performance tracking
export function trackWebVitals(metric: any) {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', metric.name, {
      event_category: 'Web Vitals',
      event_label: metric.id,
      value: Math.round(metric.name === 'CLS' ? metric.value * 1000 : metric.value),
      non_interaction: true,
      custom_map: {
        metric_rating: metric.rating,
        metric_delta: metric.delta,
      },
    });
  }
}

// Type declarations
declare global {
  interface Window {
    dataLayer: any[];
    gtag: (...args: any[]) => void;
  }
}
