"use client";

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Activity, 
  Clock, 
  Zap, 
  Eye, 
  TrendingUp, 
  AlertTriangle,
  CheckCircle,
  XCircle,
  RefreshCw
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';

interface PerformanceMetric {
  name: string;
  value: number;
  rating: 'good' | 'needs-improvement' | 'poor';
  threshold: { good: number; poor: number };
  unit: string;
  description: string;
}

interface PerformanceData {
  metrics: PerformanceMetric[];
  timestamp: number;
  url: string;
}

export function PerformanceDashboard() {
  const [performanceData, setPerformanceData] = useState<PerformanceData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  const fetchPerformanceData = async () => {
    setIsLoading(true);
    try {
      // In a real implementation, this would fetch from your analytics API
      const response = await fetch('/api/analytics/web-vitals');
      const data = await response.json();
      
      // Mock data for demonstration
      const mockMetrics: PerformanceMetric[] = [
        {
          name: 'LCP',
          value: 1200,
          rating: 'good',
          threshold: { good: 2500, poor: 4000 },
          unit: 'ms',
          description: 'Largest Contentful Paint - measures loading performance'
        },
        {
          name: 'FID',
          value: 45,
          rating: 'good',
          threshold: { good: 100, poor: 300 },
          unit: 'ms',
          description: 'First Input Delay - measures interactivity'
        },
        {
          name: 'CLS',
          value: 0.05,
          rating: 'good',
          threshold: { good: 0.1, poor: 0.25 },
          unit: '',
          description: 'Cumulative Layout Shift - measures visual stability'
        },
        {
          name: 'FCP',
          value: 800,
          rating: 'good',
          threshold: { good: 1800, poor: 3000 },
          unit: 'ms',
          description: 'First Contentful Paint - measures perceived loading speed'
        },
        {
          name: 'TTFB',
          value: 150,
          rating: 'good',
          threshold: { good: 800, poor: 1800 },
          unit: 'ms',
          description: 'Time to First Byte - measures server response time'
        },
        {
          name: 'INP',
          value: 120,
          rating: 'needs-improvement',
          threshold: { good: 200, poor: 500 },
          unit: 'ms',
          description: 'Interaction to Next Paint - measures responsiveness'
        }
      ];

      setPerformanceData({
        metrics: mockMetrics,
        timestamp: Date.now(),
        url: window.location.href
      });
      setLastUpdated(new Date());
    } catch (error) {
      console.error('Failed to fetch performance data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchPerformanceData();
  }, []);

  const getRatingColor = (rating: string) => {
    switch (rating) {
      case 'good': return 'text-green-600';
      case 'needs-improvement': return 'text-yellow-600';
      case 'poor': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const getRatingIcon = (rating: string) => {
    switch (rating) {
      case 'good': return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'needs-improvement': return <AlertTriangle className="h-4 w-4 text-yellow-600" />;
      case 'poor': return <XCircle className="h-4 w-4 text-red-600" />;
      default: return null;
    }
  };

  const getProgressValue = (metric: PerformanceMetric) => {
    const { value, threshold } = metric;
    if (value <= threshold.good) return 100;
    if (value <= threshold.poor) return 70;
    return 30;
  };

  const overallScore = performanceData 
    ? Math.round(performanceData.metrics.reduce((acc, metric) => {
        const score = metric.rating === 'good' ? 100 : metric.rating === 'needs-improvement' ? 70 : 30;
        return acc + score;
      }, 0) / performanceData.metrics.length)
    : 0;

  if (!performanceData && !isLoading) {
    return (
      <Card className="w-full max-w-4xl mx-auto">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            Performance Dashboard
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <p className="text-muted-foreground mb-4">No performance data available</p>
            <Button onClick={fetchPerformanceData}>
              Load Performance Data
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="w-full max-w-6xl mx-auto space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              Performance Dashboard
            </CardTitle>
            <div className="flex items-center gap-4">
              {lastUpdated && (
                <span className="text-sm text-muted-foreground">
                  Last updated: {lastUpdated.toLocaleTimeString()}
                </span>
              )}
              <Button 
                variant="outline" 
                size="sm" 
                onClick={fetchPerformanceData}
                disabled={isLoading}
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
                Refresh
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4">
            <div className="text-3xl font-bold">{overallScore}</div>
            <div>
              <div className="text-sm text-muted-foreground">Overall Performance Score</div>
              <Progress value={overallScore} className="w-32" />
            </div>
            <Badge variant={overallScore >= 90 ? 'default' : overallScore >= 70 ? 'secondary' : 'destructive'}>
              {overallScore >= 90 ? 'Excellent' : overallScore >= 70 ? 'Good' : 'Needs Improvement'}
            </Badge>
          </div>
        </CardContent>
      </Card>

      {/* Core Web Vitals */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {performanceData?.metrics.map((metric, index) => (
          <motion.div
            key={metric.name}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: index * 0.1 }}
          >
            <Card>
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg">{metric.name}</CardTitle>
                  {getRatingIcon(metric.rating)}
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-baseline gap-2">
                    <span className="text-2xl font-bold">
                      {metric.name === 'CLS' ? metric.value.toFixed(3) : Math.round(metric.value)}
                    </span>
                    <span className="text-sm text-muted-foreground">{metric.unit}</span>
                  </div>
                  
                  <Progress value={getProgressValue(metric)} className="h-2" />
                  
                  <div className="space-y-1">
                    <div className={`text-sm font-medium ${getRatingColor(metric.rating)}`}>
                      {metric.rating.replace('-', ' ').toUpperCase()}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {metric.description}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      Good: ≤{metric.threshold.good}{metric.unit} | 
                      Poor: >{metric.threshold.poor}{metric.unit}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>

      {/* Performance Tips */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Performance Recommendations
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {performanceData?.metrics
              .filter(metric => metric.rating !== 'good')
              .map(metric => (
                <div key={metric.name} className="flex items-start gap-3 p-3 bg-muted/50 rounded-lg">
                  <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5" />
                  <div>
                    <div className="font-medium">Improve {metric.name}</div>
                    <div className="text-sm text-muted-foreground">
                      {getRecommendation(metric.name)}
                    </div>
                  </div>
                </div>
              ))}
            {performanceData?.metrics.every(metric => metric.rating === 'good') && (
              <div className="flex items-center gap-3 p-3 bg-green-50 dark:bg-green-950/20 rounded-lg">
                <CheckCircle className="h-5 w-5 text-green-600" />
                <div className="text-green-800 dark:text-green-200">
                  All Core Web Vitals are performing well! 🎉
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

function getRecommendation(metric: string): string {
  const recommendations: Record<string, string> = {
    LCP: 'Optimize images, improve server response times, and eliminate render-blocking resources.',
    FID: 'Reduce JavaScript execution time, break up long tasks, and optimize third-party code.',
    CLS: 'Set size attributes on images and videos, avoid inserting content above existing content.',
    FCP: 'Eliminate render-blocking resources, minify CSS, and remove unused CSS.',
    TTFB: 'Optimize server configuration, use a CDN, and implement caching strategies.',
    INP: 'Optimize event handlers, reduce main thread work, and improve JavaScript performance.'
  };
  
  return recommendations[metric] || 'Optimize this metric for better user experience.';
}
