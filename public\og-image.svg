<svg width="1200" height="630" viewBox="0 0 1200 630" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bg-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4facfe;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#00f2fe;stop-opacity:1" />
    </linearGradient>
    <radialGradient id="glow1" cx="25%" cy="25%" r="50%">
      <stop offset="0%" style="stop-color:rgba(255,255,255,0.1);stop-opacity:1" />
      <stop offset="100%" style="stop-color:rgba(255,255,255,0);stop-opacity:0" />
    </radialGradient>
    <radialGradient id="glow2" cx="75%" cy="75%" r="50%">
      <stop offset="0%" style="stop-color:rgba(255,255,255,0.1);stop-opacity:1" />
      <stop offset="100%" style="stop-color:rgba(255,255,255,0);stop-opacity:0" />
    </radialGradient>
  </defs>
  
  <!-- Background -->
  <rect width="1200" height="630" fill="url(#bg-gradient)"/>
  
  <!-- Background glows -->
  <circle cx="300" cy="157" r="300" fill="url(#glow1)"/>
  <circle cx="900" cy="472" r="400" fill="url(#glow2)"/>
  
  <!-- Avatar circle -->
  <circle cx="600" cy="250" r="60" fill="rgba(255,255,255,0.2)" stroke="rgba(255,255,255,0.3)" stroke-width="4"/>
  
  <!-- Avatar text -->
  <text x="600" y="265" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="48" font-weight="bold">AK</text>
  
  <!-- Main title -->
  <text x="600" y="350" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="64" font-weight="bold">Ashish Kamat</text>
  
  <!-- Subtitle -->
  <text x="600" y="400" text-anchor="middle" fill="rgba(255,255,255,0.9)" font-family="Arial, sans-serif" font-size="32" font-weight="400">Full Stack Developer &amp; UI/UX Designer</text>
  
  <!-- Tech stack pills -->
  <g transform="translate(350, 450)">
    <!-- React -->
    <rect x="0" y="0" width="80" height="35" rx="17" fill="rgba(255,255,255,0.2)" stroke="rgba(255,255,255,0.3)"/>
    <text x="40" y="23" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="16" font-weight="500">React</text>
    
    <!-- Next.js -->
    <rect x="100" y="0" width="90" height="35" rx="17" fill="rgba(255,255,255,0.2)" stroke="rgba(255,255,255,0.3)"/>
    <text x="145" y="23" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="16" font-weight="500">Next.js</text>
    
    <!-- TypeScript -->
    <rect x="210" y="0" width="110" height="35" rx="17" fill="rgba(255,255,255,0.2)" stroke="rgba(255,255,255,0.3)"/>
    <text x="265" y="23" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="16" font-weight="500">TypeScript</text>
    
    <!-- Node.js -->
    <rect x="340" y="0" width="90" height="35" rx="17" fill="rgba(255,255,255,0.2)" stroke="rgba(255,255,255,0.3)"/>
    <text x="385" y="23" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="16" font-weight="500">Node.js</text>
  </g>
  
  <!-- Website URL -->
  <text x="1160" y="590" text-anchor="end" fill="rgba(255,255,255,0.8)" font-family="Arial, sans-serif" font-size="20" font-weight="500">ashishkamat.com.np</text>
</svg>
