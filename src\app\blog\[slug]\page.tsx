import { Metadata } from "next";
import { notFound } from "next/navigation";
import { Navigation } from "@/components/navigation";
import { Footer } from "@/components/footer";
import { BlogPostHeader } from "@/components/blog/blog-post-header";
import { BlogPostContent } from "@/components/blog/blog-post-content";
import { BlogPostSidebar } from "@/components/blog/blog-post-sidebar";
import { RelatedPosts } from "@/components/blog/related-posts";
import { BlogPostNavigation } from "@/components/blog/blog-post-navigation";
import { StructuredData } from "@/components/structured-data";
import { api } from "@/lib/api";
import { generateMetadata as generatePageMetadata, generateArticleSchema, generateBreadcrumbSchema } from "@/lib/metadata";

// Get blog post from CMS
const getBlogPost = async (slug: string) => {
  try {
    return await api.getBlogPost(slug);
  } catch (error) {
    console.error('Error fetching blog post:', error);
    return null;
  }
};

interface BlogPostPageProps {
  params: Promise<{
    slug: string;
  }>;
}

export async function generateMetadata({ params }: BlogPostPageProps): Promise<Metadata> {
  const { slug } = await params;
  const post = await getBlogPost(slug);

  if (!post) {
    return {
      title: "Post Not Found",
    };
  }

  return generatePageMetadata({
    title: post.title,
    description: post.excerpt,
    path: `/blog/${slug}`,
    type: 'article',
    publishedTime: post.publishedAt,
    modifiedTime: post.updatedAt,
    tags: post.tags,
  });
}

export default async function BlogPostPage({ params }: BlogPostPageProps) {
  const { slug } = await params;
  const post = await getBlogPost(slug);

  if (!post) {
    notFound();
  }

  const articleSchema = generateArticleSchema({
    title: post.title,
    description: post.excerpt,
    url: `https://ashishkamat.com.np/blog/${slug}`,
    publishedTime: post.publishedAt,
    modifiedTime: post.updatedAt,
    tags: post.tags,
    readingTime: post.readingTime,
  });

  const breadcrumbSchema = generateBreadcrumbSchema([
    { name: 'Home', url: 'https://ashishkamat.com.np' },
    { name: 'Blog', url: 'https://ashishkamat.com.np/blog' },
    { name: post.title, url: `https://ashishkamat.com.np/blog/${slug}` },
  ]);

  return (
    <div className="min-h-screen">
      <StructuredData data={[articleSchema, breadcrumbSchema]} />
      <Navigation />
      <main className="pt-16">
        <article>
          <BlogPostHeader post={post} />
          <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div className="grid lg:grid-cols-4 gap-12">
              <div className="lg:col-span-3">
                <BlogPostContent post={post} />
                <BlogPostNavigation currentSlug={slug} />
              </div>
              <div className="lg:col-span-1">
                <BlogPostSidebar post={post} />
              </div>
            </div>
          </div>
        </article>
        <RelatedPosts currentPost={post} />
      </main>
      <Footer />
    </div>
  );
}
